<?php

namespace App\Modules\Domain\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use Illuminate\Support\Facades\DB;
use App\Modules\Payment\Services\PaymentReimbursementService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\PaymentService\Constants\PaymentServiceType;

class DomainDeletionService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $domainDeletionService = new self;

        return $domainDeletionService;
    }

    public function query(){
        return DB::table('domain_cancellation_requests')
            ->join('registered_domains', 'registered_domains.id', '=', 'domain_cancellation_requests.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->select(
                'domain_cancellation_requests.*',
                'domains.id as domain_id',
                'domains.name as domain_name',
                'domains.status as domain_status',
                'user_contacts.user_id',
                'registered_domains.id as registered_domain_id'
            );
    }

    public function getRefundableRegistrationDomains(): array
    {
        return $this->query()
            ->where('domain_cancellation_requests.support_agent_id', '>', 0)
            ->where('domain_cancellation_requests.is_refunded', false)
            ->where('domains.status', DomainStatus::DELETED)
            ->whereNotNull('domains.deleted_at')
            ->whereNotNull('domain_cancellation_requests.feedback_date')
            ->get()->all();
    }

    public function getRefundableRenewalDomains(): array
    {
        return $this->query()
            ->where('domain_cancellation_requests.support_agent_id', '>', 0)
            ->where('domain_cancellation_requests.is_renewal', false)
            ->where('domains.status', DomainStatus::DELETED)
            ->whereNotNull('domains.deleted_at')
            ->whereNotNull('domain_cancellation_requests.feedback_date')
            ->get()->all();
    }

    public function refund(array $domains): void
    {
        foreach ($domains as $domain) {
            try {
                $registeredDomainId = $domain->registered_domain_id;
                $userId = $domain->user_id;
                $transactionType = FeeType::TRANSACTION_TYPE[FeeType::REGISTRATION];
                $description = DomainStatus::EXPIRED;

                PaymentReimbursementService::instance()->refundByRegisteredDomain(
                    $registeredDomainId,
                    $userId,
                    $transactionType,
                    $description,
                    PaymentServiceType::ACCOUNT_CREDIT,
                    true,
                    false, 
                );

                DB::table('domain_cancellation_requests')
                    ->where('registered_domain_id', $registeredDomainId)
                    ->update([
                        'is_refunded' => true,
                        'refunded_at' => now(),
                        'support_agent_id' => null,
                    ]);
                    
            } catch (\Exception $e) {
                app(AuthLogger::class)->error("Failed to refund domain name: {$domain->domain_name} - ".$e->getMessage());
            }
        }
    }


}