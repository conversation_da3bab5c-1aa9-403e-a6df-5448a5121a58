<?php

namespace App\Console\Commands\Domain;

use Illuminate\Console\Command;
use App\Modules\Domain\Services\DomainDeletionService;
use App\Modules\Payment\Services\PaymentReimbursementService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\CustomLogger\Services\AuthLogger;

class ProcessDomainDeletionRefunds extends Command
{
    protected $signature = 'domain:deletion-refund';

    protected $description = 'Process refunds for all approved domain deletions';

    public function handle()
    {
        $deletions = DomainDeletionService::instance()->query()
            ->where('domain_cancellation_requests.support_agent_id', '>', 0)
            ->where('domain_cancellation_requests.is_refunded', false)
            ->where('domains.status', DomainStatus::DELETED)
            ->whereNotNull('domains.deleted_at')
            ->whereNotNull('domain_cancellation_requests.feedback_date')
            ->get();

        if ($deletions->isEmpty()) {
            $this->info('No eligible refunds to process.');
            return Command::SUCCESS;
        }

        foreach ($deletions as $deletion) {
            try {
                $registeredDomainId = $deletion->registered_domain_id;
                $userId = $deletion->user_id;
                $transactionType = FeeType::TRANSACTION_TYPE[FeeType::REGISTRATION];
                $description = DomainStatus::EXPIRED;

                PaymentReimbursementService::instance()->refundByRegisteredDomain(
                    $registeredDomainId,
                    $userId,
                    $transactionType,
                    $description,
                    PaymentServiceType::ACCOUNT_CREDIT,
                    true,
                    false, 
                );

                DB::table('domain_cancellation_requests')
                    ->where('registered_domain_id', $registeredDomainId)
                    ->update([
                        'is_refunded' => true,
                        'refunded_at' => now(),
                        'support_agent_id' => null,
                    ]);
                    
                app(AuthLogger::class)->info("Refunded domain name: {$deletion->domain_name}");
            } catch (\Exception $e) {
                app(AuthLogger::class)->error("Failed to refund domain name: {$deletion->domain_name} - ".$e->getMessage());
            }
        }

        return Command::SUCCESS;
    }
}
